# Liste des commandes utilisées dans la partie 1 : Namespaces
question 2 : kubectl get namespaces

question 3 : kubectl apply -f namespace.yml
Difficulté : Comprendre qu'il faut soit être déjà dans l'arborescence du fichier YAML soit de préciser son chemin dans la commande

Solution : On se met dans le dossier en question
- kubectl apply -f namespace_front.yml
- kubectl apply -f namespace_middle.yml
- kubectl apply -f namespace_database.yml

question 4 : 
- kubectl describe namespace front
- kubectl describe namespace middle
- kubectl describe namespace database

question 5 : kubectl get namespaces