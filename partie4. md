Question 1: Qu'est-ce qu'un Pod en Kubernetes?
A. Un conteneur unique
B. Un groupe de conteneurs agissant comme une unité logique
C. Une méthode de gestion des secrets

Question 2: Quelle commande est utilisée pour créer un Namespace?
A. kube<PERSON>l create ns <nom-du-namespace>
B. kubectl create namespace <nom-du-namespace>
C. kubectl new namespace <nom-du-namespace>

Question 3: Lequel des composants suivants est responsable de la communication avec l'API Server?
A. kubelet
B. etcd
C. kube-proxy

Question 4: Qu'est-ce qu'un ConfigMap dans Kubernetes?
A. Un type de service
B. Une ressource pour stocker des données de configuration non sensibles
C. Un mécanisme de mise à l'échelle horizontale

Question 5: Comment exécuter un Job planifié en Kubernetes?
A. En utilisant un Job
B. En utilisant un CronJob
C. En utilisant un DaemonSet

Question 6: Qu'est-ce qu'un Deployment en Kubernetes?
A. Une méthode pour déployer des secrets dans un cluster
B. Un contrôleur responsable de la gestion des mises à jour de vos pods
C. Un conteneur unique géré par kubelet

Question 7: Quel composant est responsable de l'équilibrage de charge et du basculement entre les pods dans un Service?
A. kube-scheduler
B. kube-proxy
C. kube-controller-manager

Question 8: Comment afficher les journaux d'un pod particulier en cours d'exécution?
A. kubectl logs <pod-name>
B. kubectl view logs <pod-name>
C. kubectl get logs <pod-name>

Question 9: Que se passe-t-il lorsqu'un Pod échoue ?
A. Il est recréé par une réplication
B. Il reste en état d'échec
C. Il est déplacé vers un autre noeud

Question 10: Quelle est la différence entre ConfigMap et Secret ?
A. Aucun, ils sont identiques
B. ConfigMap est pour des données sensibles
C. Secret est pour des données sensibles

Question 11: Qu'est-ce qu'un Namespace en Kubernetes ?
A. Un type de Pod
B. Une méthode de partitionnement du cluster en plusieurs environnements virtuels
C. Une unité de stockage persistent

Question 12: Comment Kubernetes permet-il de réclamer un volume ?
A. PersistentClaim
B. AvailableVolume
C. PersistentVolumeClaim

Question 13: Quel est le rôle de l'API Server dans l'architecture de Kubernetes ?
A. Il stocke l'état souhaité du cluster et assure la persistance des données.
B. Il décide quel pod doit être programmé sur quel nœud à partir des ressources disponibles.
C. Il expose l'API RESTful de Kubernetes et agit en tant que point d'entrée pour toutes les opérations d'administration du cluster.

Question 14: Comment Kubernetes assure-t-il la sécurité et l'isolation des applications dans un cluster partagé?
A. En utilisant uniquement des firewalls pour séparer le trafic réseau entre les pods.
B. En appliquant le Role-Based Access Control (RBAC), les Pod Security Policies (PSP), et les Network Policies pour gérer les autorisations et l'isolation des pods.
C. En exécutant tous les pods dans le même espace de noms (namespace) pour une meilleure coordination.

Question 15: Quel est l'impact de la définition incorrecte des "requests" et "limits" des ressources (CPU et mémoire) pour les pods dans un cluster Kubernetes, et comment cette configuration affecte-t-elle le Scheduler et le Quality of Service (QoS) des pods ?
A. Si les "requests" sont trop basses, le Scheduler peut placer trop de pods sur un nœud, entraînant une surallocation, tandis que des "limits" trop élevées peuvent provoquer un throttling intensif des applications.
B. Les "requests" et "limits" n'ont pas d'impact sur le Scheduler, ils sont uniquement utilisés pour la facturation des ressources.
C. Des "requests" élevées garantissent la priorité dans l'allocation des ressources mais n'affectent pas le QoS des pods.


Bonus: Laquelle des affirmations suivantes n'est pas incorrecte concernant l'usage des ConfigMaps et des Secrets dans un cluster Kubernetes ?
A. Les ConfigMaps et les Secrets ne peuvent jamais être utilisés pour stocker des informations sensibles, car ils sont toujours exposés en texte clair dans le cluster.
B. Il n'est pas possible de monter une ConfigMap ou un Secret en tant que volume dans les pods.
C. Les Secrets sont stockés de manière encodée en base64 dans etcd, mais ils ne sont pas chiffrés par défaut.
