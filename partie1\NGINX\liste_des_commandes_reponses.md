# Liste des commandes utilisées dans la partie 1 : NGINX
question 3 : 
- kubectl apply -f nginx-deployment.yml --namespace=front
- kuberctl apply -f nginx-service.yaml --namespace=front

question 4 : 
- kubectl get events --namespace=front
- kubectl get pods --namespace=front

question 5 : 
- kubectl port-forward -n front svc/nginx 8080:8383

Difficulté : Comprendre que svc = le service et que nginx est le nom du service même si on le sait dans le fichier nginx-service.yaml


question 6 : 
- http://localhost:8080


# Réponses aux 3 questions :

question 7 : Les pointillés rouges correspondent au flux entre le service et le pod.

question 8 : Les pointillés violet correspondent au flux entre le service et le client.


